{"name": "prompt-evals", "version": "1.0.0", "description": "AI Prompt Chaining Management Web App", "main": "server/server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && node server.js", "client": "cd client && npm run dev", "install-all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["ai", "prompt", "evaluation", "gemini"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}