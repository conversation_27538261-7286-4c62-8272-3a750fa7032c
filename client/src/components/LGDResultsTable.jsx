import React, { useState } from 'react';
import PromptModal from './PromptModal';
import { promptsApi } from '../services/api';

// Modal component for displaying input content (transcript/competencies)
const InputContentModal = ({ isOpen, onClose, title, content }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose} // Close modal when clicking on the overlay
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80%',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()} // Prevent clicks inside modal from closing it
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0 }}>{title}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        <div style={{
          fontFamily: 'monospace',
          fontSize: '14px',
          lineHeight: '1.5',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}>
          {content}
        </div>
      </div>
    </div>
  );
};

const LGDResultsTable = ({ evaluations }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [modalState, setModalState] = useState({
    isOpen: false,
    promptData: null,
    promptName: '',
    loading: false
  });
  const [inputModalState, setInputModalState] = useState({
    isOpen: false,
    title: '',
    content: ''
  });
  const [copyStates, setCopyStates] = useState({}); // Track copy states for each evaluation

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      promptData: null,
      promptName: '',
      loading: false
    });
  };

  const openInputModal = (title, content) => {
    setInputModalState({
      isOpen: true,
      title,
      content
    });
  };

  const closeInputModal = () => {
    setInputModalState({
      isOpen: false,
      title: '',
      content: ''
    });
  };

  // Copy to clipboard function
  const handleCopyAnalysisOutput = async (evaluationId, output, evaluation) => {
    try {
      const formattedOutput = formatAnalysisOutput(output, evaluation);
      await navigator.clipboard.writeText(formattedOutput);

      // Set success state
      setCopyStates(prev => ({ ...prev, [evaluationId]: 'success' }));

      // Reset state after 2 seconds
      setTimeout(() => {
        setCopyStates(prev => ({ ...prev, [evaluationId]: null }));
      }, 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);

      // Set error state
      setCopyStates(prev => ({ ...prev, [evaluationId]: 'error' }));

      // Reset state after 2 seconds
      setTimeout(() => {
        setCopyStates(prev => ({ ...prev, [evaluationId]: null }));
      }, 2000);
    }
  };

  // Helper function to truncate text for bullet points
  const truncateText = (text, maxLength = 100) => {
    if (!text) return 'No content available';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  // Helper function to parse input data
  const parseInputData = (evaluation) => {
    try {
      let transcriptContent = 'No transcript available';
      let competenciesContent = 'No competencies available';
      let userMapping = {};

      // Try to get from input object first (full data)
      if (evaluation.input && typeof evaluation.input === 'object') {
        transcriptContent = evaluation.input.transcript || 'No transcript available';
        competenciesContent = evaluation.input.competencies || 'No competencies available';
      } else if (evaluation.formattedInput) {
        // Fallback to formattedInput parsing
        const lines = evaluation.formattedInput.split('\n');
        const transcriptLine = lines.find(line => line.startsWith('Transcript:'));
        const competenciesLine = lines.find(line => line.startsWith('Competencies:'));

        transcriptContent = transcriptLine ? transcriptLine.replace('Transcript:', '').trim() : 'No transcript available';
        competenciesContent = competenciesLine ? competenciesLine.replace('Competencies:', '').trim() : 'No competencies available';
      }

      // Extract user_name from transcriptContent if it's a valid JSON string
      if (transcriptContent !== 'No transcript available' && transcriptContent.startsWith('[')) {
        try {
          const parsedTranscript = JSON.parse(transcriptContent);
          if (Array.isArray(parsedTranscript) && parsedTranscript.length > 0) {
            parsedTranscript.forEach(user => {
              if (userMapping[user.user_id]) return;

              userMapping[user.user_id] = user.user_name;
            });
          }
        } catch (jsonError) {
          console.warn('Could not parse transcript as JSON:', jsonError);
        }
      }

      return {
        transcript: transcriptContent,
        competencies: competenciesContent,
        userMapping: userMapping
      };
    } catch (error) {
      console.error('Error parsing input data:', error);
      return {
        transcript: 'Error parsing transcript',
        competencies: 'Error parsing competencies',
        userName: 'Error'
      };
    }
  };

  const handlePromptVersionClick = async (promptId, version, promptName, evaluation = null) => {
    setModalState({
      isOpen: true,
      promptData: null,
      promptName,
      loading: true
    });

    try {
      // First try to use stored content from evaluation if available
      if (evaluation) {
        const storedContent = promptId === 3 ? evaluation.prompt1Content : evaluation.prompt2Content;
        if (storedContent) {
          setModalState(prev => ({
            ...prev,
            promptData: {
              content: storedContent,
              version: version,
              updatedAt: evaluation.timestamp
            },
            loading: false
          }));
          return;
        }
      }

      // Fallback to API call for historical versions
      const response = await promptsApi.getVersion(promptId, version);
      setModalState(prev => ({
        ...prev,
        promptData: response.data,
        loading: false
      }));
    } catch (error) {
      console.error('Error fetching prompt version:', error);
      setModalState(prev => ({
        ...prev,
        loading: false
      }));
    }
  };

  const formatAnalysisOutput = (output, evaluation) => {
    let parsedOutput;
    if (typeof output === 'string') {
      try {
        parsedOutput = JSON.parse(output);
      } catch (e) {
        return output; // Return original if not valid JSON
      }
    } else if (typeof output === 'object' && output !== null) {
      parsedOutput = output;
    } else {
      return JSON.stringify(output, null, 2); // Handle other types
    }

    // Extract user_name using the same logic as parseInputData
    const inputData = parseInputData(evaluation);
    const userMapping = inputData.userMapping;

    if (parsedOutput && parsedOutput.result && Array.isArray(parsedOutput.result)) {
      const reformattedOutput = parsedOutput.result.map(item => {
        const competencies = item.detail_scores.map(comp => {
          const details = comp.aspect_details.map(aspect => {
            return {
              level: aspect.level,
              key_behaviours: [
                {
                  name: aspect.name,
                  evidences: aspect.evidences.map(evidence => ({
                    evidence: evidence.evidence,
                    description: evidence.description,
                    timestamp: evidence.timestamp
                  }))
                }
              ]
            };
          });

          return {
            name: comp.competency_name,
            score: comp.average_score,
            details: details
          };
        });

        const userName = userMapping[item.user_id] || item.user_id;

        return {
          user_id: item.user_id,
          user_name: userName,
          competencies: competencies
        };
      });
      return JSON.stringify(reformattedOutput, null, 2);
    }

    return JSON.stringify(parsedOutput, null, 2); // Fallback if structure is unexpected
  };

  if (!evaluations || evaluations.length === 0) {
    return (
      <div style={{
        textAlign: 'center',
        padding: '40px',
        color: '#6c757d',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #dee2e6'
      }}>
        <h3>No LGD Analysis Results</h3>
        <p>Run an LGD analysis to see results here.</p>
      </div>
    );
  }

  return (
    <div>
      <h2 style={{ marginBottom: '20px', color: '#495057' }}>
        📊 LGD Analysis Results ({evaluations.length})
      </h2>
      
      <div style={{
        border: '1px solid #dee2e6',
        borderRadius: '8px',
        overflow: 'hidden',
        backgroundColor: 'white'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead style={{ backgroundColor: '#f8f9fa' }}>
            <tr>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Prompt Versions
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Input Summary
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{
                  borderBottom: '1px solid #eee',
                  backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white'
                }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <div>
                      P3: <button
                        onClick={() => handlePromptVersionClick(3, evaluation.prompt1Version, 'LGD Analysis Prompt', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt1Version}
                      </button>
                    </div>
                    <div>
                      P4: <button
                        onClick={() => handlePromptVersionClick(4, evaluation.prompt2Version, 'LGD Formatting Prompt', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt2Version}
                      </button>
                    </div>
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {(() => {
                      const inputData = parseInputData(evaluation);
                      return (
                        <div style={{ fontSize: '12px', color: '#495057' }}>
                          <div style={{ marginBottom: '4px' }}>
                            • <strong>Transcript:</strong> {truncateText(inputData.transcript, 50)}
                          </div>
                          <div>
                            • <strong>Competencies:</strong> {truncateText(inputData.competencies, 50)}
                          </div>
                        </div>
                      );
                    })()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <button
                      onClick={() => toggleRow(evaluation.id)}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #007bff',
                        borderRadius: '4px',
                        backgroundColor: expandedRow === evaluation.id ? '#007bff' : 'white',
                        color: expandedRow === evaluation.id ? 'white' : '#007bff',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      {expandedRow === evaluation.id ? 'Hide' : 'Show'} Details
                    </button>
                  </td>
                </tr>
                
                {expandedRow === evaluation.id && (
                  <tr>
                    <td colSpan="4" style={{ padding: '20px', backgroundColor: '#f8f9fa' }}>
                      <div style={{ display: 'grid', gap: '20px' }}>
                        <div>
                          <h4 style={{ marginBottom: '10px', color: '#495057' }}>📝 Input Data</h4>
                          <div style={{
                            backgroundColor: 'white',
                            padding: '15px',
                            borderRadius: '4px',
                            border: '1px solid #dee2e6'
                          }}>
                            {(() => {
                              const inputData = parseInputData(evaluation);
                              return (
                                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                                    <span style={{ fontSize: '14px', color: '#495057' }}>
                                      • <strong>Transcript:</strong> {truncateText(inputData.transcript, 80)}
                                    </span>
                                    <button
                                      onClick={() => openInputModal('LGD Transcript', inputData.transcript)}
                                      style={{
                                        padding: '4px 8px',
                                        border: '1px solid #007bff',
                                        borderRadius: '4px',
                                        backgroundColor: 'white',
                                        color: '#007bff',
                                        cursor: 'pointer',
                                        fontSize: '11px',
                                        whiteSpace: 'nowrap'
                                      }}
                                    >
                                      View Full
                                    </button>
                                  </div>
                                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                                    <span style={{ fontSize: '14px', color: '#495057' }}>
                                      • <strong>Competencies:</strong> {truncateText(inputData.competencies, 80)}
                                    </span>
                                    <button
                                      onClick={() => openInputModal('Competency Guidelines', inputData.competencies)}
                                      style={{
                                        padding: '4px 8px',
                                        border: '1px solid #007bff',
                                        borderRadius: '4px',
                                        backgroundColor: 'white',
                                        color: '#007bff',
                                        cursor: 'pointer',
                                        fontSize: '11px',
                                        whiteSpace: 'nowrap'
                                      }}
                                    >
                                      View Full
                                    </button>
                                  </div>
                                </div>
                              );
                            })()}
                          </div>
                        </div>
                        
                        <div>
                          <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: '10px'
                          }}>
                            <h4 style={{ margin: 0, color: '#495057' }}>🎯 Analysis Output</h4>
                            <button
                              onClick={() => handleCopyAnalysisOutput(evaluation.id, evaluation.output, evaluation)}
                              style={{
                                padding: '4px 8px',
                                border: '1px solid #28a745',
                                borderRadius: '4px',
                                backgroundColor: copyStates[evaluation.id] === 'success' ? '#28a745' :
                                                copyStates[evaluation.id] === 'error' ? '#dc3545' : 'white',
                                color: copyStates[evaluation.id] === 'success' ? 'white' :
                                       copyStates[evaluation.id] === 'error' ? 'white' : '#28a745',
                                cursor: 'pointer',
                                fontSize: '11px',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                                transition: 'all 0.2s ease'
                              }}
                              title="Copy analysis output to clipboard"
                            >
                              {copyStates[evaluation.id] === 'success' ? '✓ Copied!' :
                               copyStates[evaluation.id] === 'error' ? '✗ Error' : '📋 Copy'}
                            </button>
                          </div>
                          <pre style={{
                            backgroundColor: 'white',
                            padding: '15px',
                            borderRadius: '4px',
                            border: '1px solid #dee2e6',
                            fontSize: '12px',
                            overflow: 'auto',
                            maxHeight: '400px',
                            whiteSpace: 'pre-wrap',
                            wordBreak: 'break-word'
                          }}>
                            {formatAnalysisOutput(evaluation.output, evaluation)}
                          </pre>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      <PromptModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        promptData={modalState.promptData}
        promptName={modalState.promptName}
        loading={modalState.loading}
      />

      <InputContentModal
        isOpen={inputModalState.isOpen}
        onClose={closeInputModal}
        title={inputModalState.title}
        content={inputModalState.content}
      />
    </div>
  );
};

export default LGDResultsTable;
